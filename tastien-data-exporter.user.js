// ==UserScript==
// @name         Tastien数据导出助手
// @namespace    http://tampermonkey.net/
// @version      1.4.1
// @description  自动导出Tastien平台指定月份范围的业务数据
// <AUTHOR>
// @match        https://tastien.tastientech.com/portal/expand-store/developStores/bunk*
// @grant        none
// @updateURL
// @downloadURL
// ==/UserScript==

/*
 * 版本: 1.4.1
 * 变更记录:
 * - 2025-01-01: 初始版本，实现基础的月份范围数据导出功能
 * - 2025-01-01: 修改为直接在页面日期输入框中填入日期，不再使用URL跳转
 * - 2025-01-01: 修复querySelector重写导致页面冲突的问题，简化按钮查找逻辑
 * - 2025-01-01: 添加暂停按钮功能，确保日期格式为YYYY-MM-DD
 * - 2025-01-01: 修复日期输入问题，改进React组件值设置方法，注意3个月时间跨度限制
 * - 2025-01-01: 重写日期选择逻辑，支持Ant Design日历面板点击选择日期
 * - 2025-01-01: 修复第二个月日期输入问题，改进同月和跨月日期选择逻辑
 * - 2025-01-01: 恢复使用实际月末日期，而不是固定28日
 * - 2025-01-01: 修复第二次输入时开始日期错误输入到结束日期框的问题
 * - 2025-01-01: 重大重构：改为直接输入日期文本，不再使用日历控件点击方式
 * - 2025-01-01: 代码清理和优化，移除冗余代码，改善代码结构
 */

(function() {
    'use strict';

    // 全局变量
    let isProcessing = false;
    let isPaused = false;
    let currentMonth = 1;
    let endMonth = 12;
    let currentYear = new Date().getFullYear();
    let pauseButton = null;

    // 工具函数
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const element = document.querySelector(selector);
            if (element) {
                resolve(element);
                return;
            }

            const observer = new MutationObserver((_, obs) => {
                const element = document.querySelector(selector);
                if (element) {
                    obs.disconnect();
                    resolve(element);
                }
            });

            observer.observe(document.body, {
                childList: true,
                subtree: true
            });

            setTimeout(() => {
                observer.disconnect();
                reject(new Error(`Element ${selector} not found within ${timeout}ms`));
            }, timeout);
        });
    }

    function getLastDayOfMonth(year, month) {
        return new Date(year, month, 0).getDate().toString().padStart(2, '0');
    }

    function simulateDirectInput(input, value) {
        console.log(`直接输入日期: ${value} 到输入框`);
        console.log(`输入框初始状态: value="${input.value}", placeholder="${input.placeholder}"`);

        // 聚焦输入框
        input.focus();
        console.log('已聚焦输入框');

        // 选中所有文本并删除
        input.select();
        input.setSelectionRange(0, input.value.length);

        // 清空现有值
        input.value = '';

        // 使用多种方法设置值
        const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value').set;
        nativeInputValueSetter.call(input, value);

        // 创建更完整的事件序列
        const events = [
            new Event('focus', { bubbles: true }),
            new KeyboardEvent('keydown', { bubbles: true, key: 'Backspace' }),
            new KeyboardEvent('keyup', { bubbles: true, key: 'Backspace' }),
            new Event('input', { bubbles: true, inputType: 'insertText', data: value }),
            new KeyboardEvent('keydown', { bubbles: true, key: 'Enter' }),
            new KeyboardEvent('keyup', { bubbles: true, key: 'Enter' }),
            new Event('change', { bubbles: true }),
            new Event('blur', { bubbles: true })
        ];

        // 为每个事件设置正确的target和currentTarget
        events.forEach(event => {
            Object.defineProperty(event, 'target', { value: input, enumerable: true });
            Object.defineProperty(event, 'currentTarget', { value: input, enumerable: true });
        });

        // 依次触发事件，每个事件之间有小延迟
        events.forEach((event, index) => {
            setTimeout(() => {
                console.log(`触发事件: ${event.type}`);
                input.dispatchEvent(event);
            }, index * 10);
        });

        // 最后失焦
        setTimeout(() => {
            input.blur();
            console.log(`输入框最终值: "${input.value}"`);
        }, events.length * 10 + 50);
    }

    // 备用的键盘模拟输入方法
    async function simulateKeyboardInput(input, value) {
        console.log(`使用键盘模拟输入: ${value}`);

        input.focus();
        input.select();

        // 清空现有内容
        input.dispatchEvent(new KeyboardEvent('keydown', { bubbles: true, key: 'Backspace' }));
        input.value = '';
        input.dispatchEvent(new Event('input', { bubbles: true }));

        // 逐字符输入
        for (let i = 0; i < value.length; i++) {
            const char = value[i];

            // 键盘按下
            input.dispatchEvent(new KeyboardEvent('keydown', {
                bubbles: true,
                key: char,
                code: `Key${char.toUpperCase()}`,
                charCode: char.charCodeAt(0),
                keyCode: char.charCodeAt(0)
            }));

            // 字符输入
            input.dispatchEvent(new KeyboardEvent('keypress', {
                bubbles: true,
                key: char,
                charCode: char.charCodeAt(0),
                keyCode: char.charCodeAt(0)
            }));

            // 更新值
            input.value += char;

            // 输入事件
            input.dispatchEvent(new Event('input', {
                bubbles: true,
                inputType: 'insertText',
                data: char
            }));

            // 键盘释放
            input.dispatchEvent(new KeyboardEvent('keyup', {
                bubbles: true,
                key: char,
                code: `Key${char.toUpperCase()}`,
                charCode: char.charCodeAt(0),
                keyCode: char.charCodeAt(0)
            }));

            // 小延迟模拟真实输入
            await new Promise(resolve => setTimeout(resolve, 20));
        }

        // 触发change和blur事件
        input.dispatchEvent(new Event('change', { bubbles: true }));
        input.blur();
        input.dispatchEvent(new Event('blur', { bubbles: true }));

        console.log(`键盘输入完成，最终值: "${input.value}"`);
    }

    // UI组件创建
    function createFloatingButton() {
        const button = document.createElement('div');
        button.id = 'tastien-export-btn';
        button.innerHTML = '📊';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;

        button.addEventListener('click', showModal);
        document.body.appendChild(button);
    }

    function createPauseButton() {
        pauseButton = document.createElement('div');
        pauseButton.id = 'tastien-pause-btn';
        pauseButton.innerHTML = isPaused ? '▶️' : '⏸️';
        pauseButton.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: ${isPaused ? '#52c41a' : '#ff4d4f'};
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            opacity: ${isProcessing ? '1' : '0.3'};
            pointer-events: ${isProcessing ? 'auto' : 'none'};
        `;

        pauseButton.addEventListener('click', togglePause);
        document.body.appendChild(pauseButton);
    }

    function updatePauseButton() {
        if (pauseButton) {
            pauseButton.innerHTML = isPaused ? '▶️' : '⏸️';
            pauseButton.style.background = isPaused ? '#52c41a' : '#ff4d4f';
            pauseButton.style.opacity = isProcessing ? '1' : '0.3';
            pauseButton.style.pointerEvents = isProcessing ? 'auto' : 'none';
        }
    }

    function showModal() {
        if (isProcessing) {
            alert('正在处理中，请等待完成或点击暂停按钮');
            return;
        }

        const modal = document.createElement('div');
        modal.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 10001;
            display: flex;
            align-items: center;
            justify-content: center;
        `;

        modal.innerHTML = `
            <div style="background: white; padding: 20px; border-radius: 8px; width: 400px; max-width: 90%;">
                <h3 style="margin: 0 0 15px 0; color: #333;">选择导出月份范围</h3>
                <div style="margin: 10px 0;">
                    <label style="display: block; margin-bottom: 5px; color: #666;">开始月份:</label>
                    <select id="start-month" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        ${Array.from({length: 12}, (_, i) => `<option value="${i+1}">${i+1}月</option>`).join('')}
                    </select>
                </div>
                <div style="margin: 10px 0;">
                    <label style="display: block; margin-bottom: 5px; color: #666;">结束月份:</label>
                    <select id="end-month" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                        ${Array.from({length: 12}, (_, i) => `<option value="${i+1}" ${i === 11 ? 'selected' : ''}>${i+1}月</option>`).join('')}
                    </select>
                </div>
                <div style="margin: 10px 0; padding: 10px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px; font-size: 12px;">
                    <strong>注意:</strong> 系统限制时间跨度不能超过3个月，脚本会自动按月逐个处理<br>
                    <strong>日期范围:</strong> 每月从1日到月末（如：2025-01-01 至 2025-01-31）
                </div>
                <div style="margin-top: 20px; text-align: right;">
                    <button id="cancel-btn" style="margin-right: 10px; padding: 8px 16px; border: 1px solid #ddd; background: white; border-radius: 4px; cursor: pointer;">取消</button>
                    <button id="start-btn" style="padding: 8px 16px; border: none; background: #1890ff; color: white; border-radius: 4px; cursor: pointer;">开始导出</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        document.getElementById('cancel-btn').addEventListener('click', () => {
            document.body.removeChild(modal);
        });

        document.getElementById('start-btn').addEventListener('click', () => {
            const startMonth = parseInt(document.getElementById('start-month').value);
            const endMonth = parseInt(document.getElementById('end-month').value);
            
            if (startMonth > endMonth) {
                alert('开始月份不能大于结束月份');
                return;
            }
            
            document.body.removeChild(modal);
            startExport(startMonth, endMonth);
        });
    }

    function togglePause() {
        if (!isProcessing) return;
        
        isPaused = !isPaused;
        updatePauseButton();
        console.log(isPaused ? '已暂停' : '已继续');
    }

    // 核心导出逻辑
    async function startExport(startMonth, endMonth) {
        isProcessing = true;
        isPaused = false;
        currentMonth = startMonth;
        endMonth = endMonth;
        updatePauseButton();

        console.log(`开始导出 ${startMonth} 月到 ${endMonth} 月的数据`);

        for (let month = startMonth; month <= endMonth; month++) {
            if (isPaused) {
                console.log('导出已暂停');
                while (isPaused) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
                console.log('导出已继续');
            }

            try {
                await processMonth(month);
            } catch (error) {
                console.error(`处理 ${month} 月数据时出错:`, error);
                alert(`处理 ${month} 月数据时出错: ${error.message}`);
                break;
            }
        }

        isProcessing = false;
        isPaused = false;
        updatePauseButton();
        console.log('所有月份处理完成');
    }

    async function processMonth(month) {
        console.log(`正在处理 ${month} 月数据...`);

        const startDate = `${currentYear}-${month.toString().padStart(2, '0')}-01`;
        const endDate = `${currentYear}-${month.toString().padStart(2, '0')}-${getLastDayOfMonth(currentYear, month)}`;

        console.log(`日期范围: ${startDate} 到 ${endDate}`);

        try {
            await fillDateInputs(startDate, endDate);
            await clickSearchButton();
            await clickExportButton();
        } catch (error) {
            isProcessing = false;
            isPaused = false;
            updatePauseButton();
            alert(`处理 ${month} 月数据时出错: ${error.message}`);
        }
    }

    async function fillDateInputs(startDate, endDate) {
        console.log(`填入日期: ${startDate} 到 ${endDate}`);

        try {
            const startInput = await waitForElement('input[date-range="start"]');
            const endInput = await waitForElement('input[date-range="end"]');

            console.log('找到日期输入框');
            console.log(`开始日期输入框信息: tagName=${startInput.tagName}, type=${startInput.type}, className=${startInput.className}`);
            console.log(`结束日期输入框信息: tagName=${endInput.tagName}, type=${endInput.type}, className=${endInput.className}`);

            // 确保任何打开的日历都被关闭
            document.body.click();
            await new Promise(resolve => setTimeout(resolve, 500));

            // 方法1：尝试直接输入
            console.log('=== 方法1：直接输入开始日期 ===');
            simulateDirectInput(startInput, startDate);
            await new Promise(resolve => setTimeout(resolve, 1200));

            console.log('=== 方法1：直接输入结束日期 ===');
            simulateDirectInput(endInput, endDate);
            await new Promise(resolve => setTimeout(resolve, 1200));

            console.log(`方法1结果 - 开始日期: "${startInput.value}", 结束日期: "${endInput.value}"`);

            // 检查第一种方法是否成功
            if (startInput.value !== startDate || endInput.value !== endDate) {
                console.warn('方法1失败，尝试方法2：键盘模拟输入...');

                await new Promise(resolve => setTimeout(resolve, 500));

                console.log('=== 方法2：键盘模拟输入开始日期 ===');
                await simulateKeyboardInput(startInput, startDate);
                await new Promise(resolve => setTimeout(resolve, 800));

                console.log('=== 方法2：键盘模拟输入结束日期 ===');
                await simulateKeyboardInput(endInput, endDate);
                await new Promise(resolve => setTimeout(resolve, 800));

                console.log(`方法2结果 - 开始日期: "${startInput.value}", 结束日期: "${endInput.value}"`);
            }

            // 最终检查
            if (startInput.value !== startDate || endInput.value !== endDate) {
                console.warn('方法2也失败，尝试方法3：强制设置...');

                // 方法3：强制设置并触发所有可能的事件
                startInput.value = startDate;
                endInput.value = endDate;

                // 触发所有可能需要的事件
                [startInput, endInput].forEach(input => {
                    ['input', 'change', 'blur', 'keyup', 'keydown'].forEach(eventType => {
                        input.dispatchEvent(new Event(eventType, { bubbles: true }));
                    });
                });

                await new Promise(resolve => setTimeout(resolve, 500));
                console.log(`方法3结果 - 开始日期: "${startInput.value}", 结束日期: "${endInput.value}"`);
            }

            // 最终验证
            const finalStartValue = startInput.value;
            const finalEndValue = endInput.value;

            console.log(`=== 最终结果 ===`);
            console.log(`开始日期最终值: "${finalStartValue}" (期望: "${startDate}")`);
            console.log(`结束日期最终值: "${finalEndValue}" (期望: "${endDate}")`);

            if (finalStartValue !== startDate || finalEndValue !== endDate) {
                console.error('所有方法都失败了，日期输入不成功');
                throw new Error(`日期输入失败 - 开始日期: "${finalStartValue}" != "${startDate}", 结束日期: "${finalEndValue}" != "${endDate}"`);
            }

            console.log('日期填入完成');

        } catch (error) {
            console.error('填入日期失败:', error);
            throw error;
        }
    }

    async function clickSearchButton() {
        console.log('查找搜索按钮...');

        const buttons = Array.from(document.querySelectorAll('button, .ant-btn'));
        const searchBtn = buttons.find(btn => btn.textContent.includes('搜') && btn.textContent.includes('索'));

        if (!searchBtn) {
            throw new Error('未找到搜索按钮');
        }

        searchBtn.click();
        console.log('已点击搜索按钮');

        for (let i = 0; i < 25; i++) {
            if (isPaused) return;
            await new Promise(resolve => setTimeout(resolve, 200));
        }
    }

    async function clickExportButton() {
        console.log('等待导出按钮...');

        const buttons = Array.from(document.querySelectorAll('button, .ant-btn'));
        const exportBtn = buttons.find(btn => btn.textContent.includes('导出业务数据'));

        if (!exportBtn) {
            throw new Error('未找到导出按钮');
        }

        exportBtn.click();
        console.log('已点击导出按钮');

        for (let i = 0; i < 50; i++) {
            if (isPaused) return;
            await new Promise(resolve => setTimeout(resolve, 200));
        }

        console.log(`${currentMonth} 月数据导出完成`);
    }

    // 初始化
    function init() {
        if (document.getElementById('tastien-export-btn')) return;

        createFloatingButton();
        createPauseButton();
        console.log('Tastien数据导出助手已加载');
    }

    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
