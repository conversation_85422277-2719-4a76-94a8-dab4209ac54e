// ==UserScript==
// @name         Tastien数据导出助手
// @namespace    http://tampermonkey.net/
// @version      1.4.0
// @description  自动导出Tastien平台指定月份范围的业务数据
// <AUTHOR>
// @match        https://tastien.tastientech.com/portal/expand-store/developStores/bunk*
// @grant        none
// @updateURL
// @downloadURL
// ==/UserScript==

/*
 * 版本: 1.9.0
 * 变更记录:
 * - 2025-01-01: 初始版本，实现基础的月份范围数据导出功能
 * - 2025-01-01: 修改为直接在页面日期输入框中填入日期，不再使用URL跳转
 * - 2025-01-01: 修复querySelector重写导致页面冲突的问题，简化按钮查找逻辑
 * - 2025-01-01: 添加暂停按钮功能，确保日期格式为YYYY-MM-DD
 * - 2025-01-01: 修复日期输入问题，改进React组件值设置方法，注意3个月时间跨度限制
 * - 2025-01-01: 重写日期选择逻辑，支持Ant Design日历面板点击选择日期
 * - 2025-01-01: 修复第二个月日期输入问题，改进同月和跨月日期选择逻辑
 * - 2025-01-01: 恢复使用实际月末日期，而不是固定28日
 * - 2025-01-01: 修复第二次输入时开始日期错误输入到结束日期框的问题
 * - 2025-01-01: 重大重构：改为直接输入日期文本，不再使用日历控件点击方式
 * - 2025-01-01: v1.5.0 改进日期输入方式，使用逐字符输入模拟真实用户行为，增强Ant Design兼容性
 * - 2025-01-01: v1.6.0 修复日历面板关闭问题，改进日期输入验证和清理逻辑，增强稳定性
 * - 2025-01-01: v1.7.0 简化为直接写入日期方式，移除复杂的输入模拟，提高稳定性和速度
 * - 2025-01-01: v1.8.0 改进按钮点击方式，使用模拟鼠标事件，日期输入后自动关闭面板，增强稳定性
 * - 2025-01-01: v1.9.0 修复点击搜索按钮时日期消失问题，增强日期输入稳定性和验证机制
 */

(function() {
    'use strict';

    // 全局变量
    let isProcessing = false;
    let isPaused = false;
    let currentMonth = 1;
    let endMonth = 12;
    let currentYear = new Date().getFullYear();
    let pauseButton = null;


    // 创建悬浮按钮
    function createFloatingButton() {
        const button = document.createElement('div');
        button.id = 'tastien-export-btn';
        button.innerHTML = '📊';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;

        button.addEventListener('click', showModal);
        document.body.appendChild(button);
    }

    // 创建暂停按钮
    function createPauseButton() {
        pauseButton = document.createElement('div');
        pauseButton.id = 'tastien-pause-btn';
        pauseButton.innerHTML = isPaused ? '▶️' : '⏸️';
        pauseButton.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: ${isPaused ? '#52c41a' : '#ff4d4f'};
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            opacity: ${isProcessing ? '1' : '0.3'};
            pointer-events: ${isProcessing ? 'auto' : 'none'};
        `;

        pauseButton.addEventListener('click', togglePause);
        document.body.appendChild(pauseButton);
    }

    // 切换暂停状态
    function togglePause() {
        if (!isProcessing) return;

        isPaused = !isPaused;
        pauseButton.innerHTML = isPaused ? '▶️' : '⏸️';
        pauseButton.style.background = isPaused ? '#52c41a' : '#ff4d4f';

        console.log(isPaused ? '已暂停导出' : '继续导出');

        if (!isPaused) {
            // 如果从暂停状态恢复，继续处理当前月份
            setTimeout(() => processMonth(currentMonth), 1000);
        }
    }

    // 更新暂停按钮状态
    function updatePauseButton() {
        if (pauseButton) {
            pauseButton.style.opacity = isProcessing ? '1' : '0.3';
            pauseButton.style.pointerEvents = isProcessing ? 'auto' : 'none';

            if (!isProcessing) {
                pauseButton.innerHTML = '⏸️';
                pauseButton.style.background = '#ff4d4f';
                isPaused = false;
            }
        }
    }

    // 创建模态框
    function showModal() {
        if (isProcessing) {
            alert('正在处理中，请稍候...');
            return;
        }

        const modal = document.createElement('div');
        modal.id = 'tastien-modal';
        modal.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10001; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 20px; border-radius: 8px; width: 350px;">
                    <h3>选择导出月份范围</h3>
                    <div style="margin: 10px 0; padding: 10px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px; font-size: 12px;">
                        <strong>注意:</strong> 系统限制时间跨度不能超过3个月，脚本会自动按月逐个处理<br>
                        <strong>日期范围:</strong> 每月从1日到月末（如：2025-01-01 至 2025-01-31）
                    </div>
                    <div style="margin: 10px 0;">
                        <label>开始月份 (1-12): </label>
                        <input type="number" id="start-month" min="1" max="12" value="1" style="width: 60px;">
                    </div>
                    <div style="margin: 10px 0;">
                        <label>结束月份 (1-12): </label>
                        <input type="number" id="end-month" min="1" max="12" value="12" style="width: 60px;">
                    </div>
                    <div style="margin: 20px 0; text-align: center;">
                        <button id="start-export" style="margin-right: 10px; padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">开始导出</button>
                        <button id="cancel-export" style="padding: 8px 16px; background: #ccc; color: black; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        document.getElementById('start-export').addEventListener('click', startExport);
        document.getElementById('cancel-export').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    }

    // 开始导出流程
    function startExport() {
        const startMonth = parseInt(document.getElementById('start-month').value);
        const endMonthValue = parseInt(document.getElementById('end-month').value);

        if (startMonth < 1 || startMonth > 12 || endMonthValue < 1 || endMonthValue > 12) {
            alert('请输入有效的月份 (1-12)');
            return;
        }

        if (startMonth > endMonthValue) {
            alert('开始月份不能大于结束月份');
            return;
        }

        // 关闭模态框
        const modal = document.getElementById('tastien-modal');
        document.body.removeChild(modal);

        // 开始处理
        isProcessing = true;
        isPaused = false;
        currentMonth = startMonth;
        endMonth = endMonthValue;

        // 创建暂停按钮
        createPauseButton();
        updatePauseButton();

        console.log(`开始导出 ${startMonth} 月到 ${endMonthValue} 月的数据`);
        processMonth(currentMonth);
    }

    // 处理单个月份
    async function processMonth(month) {
        // 检查是否暂停
        if (isPaused) {
            console.log('导出已暂停');
            return;
        }

        if (month > endMonth) {
            isProcessing = false;
            isPaused = false;
            updatePauseButton();
            alert('所有月份数据导出完成！');
            return;
        }

        console.log(`正在处理 ${month} 月数据...`);

        // 构建日期 - 每月从1日到月末，确保格式为 YYYY-MM-DD
        const startDate = `${currentYear}-${month.toString().padStart(2, '0')}-01`;
        const endDate = `${currentYear}-${month.toString().padStart(2, '0')}-${getLastDayOfMonth(currentYear, month)}`;

        try {
            // 填入日期到输入框
            await fillDateInputs(startDate, endDate);

            // 等待2秒后自动点击按钮
            setTimeout(autoClickButtons, 2000);

        } catch (error) {
            console.error('处理月份失败:', error);
            isProcessing = false;
            isPaused = false;
            updatePauseButton();
            alert(`处理 ${month} 月数据时出错: ${error.message}`);
        }
    }
    // 获取月份最后一天
    function getLastDayOfMonth(year, month) {
        return new Date(year, month, 0).getDate().toString().padStart(2, '0');
    }





    // 增强的日期写入函数 - 确保React组件状态稳定
    function setDateDirectly(input, value) {
        console.log(`增强写入日期: ${value}`);

        try {
            // 获取React的原生setter
            const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value').set;

            // 先聚焦输入框
            input.focus();

            // 触发focus事件
            input.dispatchEvent(new FocusEvent('focus', { bubbles: true }));
            input.dispatchEvent(new FocusEvent('focusin', { bubbles: true }));

            // 清空现有值
            nativeInputValueSetter.call(input, '');
            input.dispatchEvent(new Event('input', { bubbles: true }));

            // 设置新值
            nativeInputValueSetter.call(input, value);

            // 触发完整的事件序列让React感知变化
            input.dispatchEvent(new Event('input', {
                bubbles: true,
                cancelable: true
            }));

            input.dispatchEvent(new Event('change', {
                bubbles: true,
                cancelable: true
            }));

            // 触发键盘事件（模拟用户输入）
            input.dispatchEvent(new KeyboardEvent('keydown', {
                bubbles: true,
                key: 'Enter'
            }));

            input.dispatchEvent(new KeyboardEvent('keyup', {
                bubbles: true,
                key: 'Enter'
            }));

            // 失焦事件
            input.dispatchEvent(new FocusEvent('blur', { bubbles: true }));
            input.dispatchEvent(new FocusEvent('focusout', { bubbles: true }));

            console.log(`增强写入完成，当前值: "${input.value}"`);
            return input.value === value;

        } catch (error) {
            console.error('增强写入日期时出错:', error);
            return false;
        }
    }

    // 模拟鼠标点击函数
    function simulateMouseClick(element) {
        console.log(`模拟鼠标点击: ${element.tagName}${element.className ? '.' + element.className.split(' ').join('.') : ''}`);

        try {
            // 确保元素可见和可点击
            if (element.style.display === 'none' || element.style.visibility === 'hidden') {
                console.warn('元素不可见，尝试显示...');
                element.style.display = '';
                element.style.visibility = 'visible';
            }

            // 获取元素位置
            const rect = element.getBoundingClientRect();
            const centerX = rect.left + rect.width / 2;
            const centerY = rect.top + rect.height / 2;

            console.log(`元素位置: (${centerX}, ${centerY}), 尺寸: ${rect.width}x${rect.height}`);

            // 创建完整的鼠标事件序列
            const mouseDownEvent = new MouseEvent('mousedown', {
                bubbles: true,
                cancelable: true,
                clientX: centerX,
                clientY: centerY,
                button: 0
            });

            const mouseUpEvent = new MouseEvent('mouseup', {
                bubbles: true,
                cancelable: true,
                clientX: centerX,
                clientY: centerY,
                button: 0
            });

            const clickEvent = new MouseEvent('click', {
                bubbles: true,
                cancelable: true,
                clientX: centerX,
                clientY: centerY,
                button: 0
            });

            // 依次触发事件
            element.dispatchEvent(mouseDownEvent);
            console.log('触发 mousedown 事件');

            setTimeout(() => {
                element.dispatchEvent(mouseUpEvent);
                console.log('触发 mouseup 事件');
            }, 50);

            setTimeout(() => {
                element.dispatchEvent(clickEvent);
                console.log('触发 click 事件');
            }, 100);

            // 备用：直接调用click方法
            setTimeout(() => {
                element.click();
                console.log('执行直接 click() 方法');
            }, 150);

            console.log('✅ 模拟鼠标点击完成');
            return true;

        } catch (error) {
            console.error('模拟鼠标点击失败:', error);
            return false;
        }
    }

    // 简化的日期输入函数 - 直接写入方式
    async function fillDateInputs(startDate, endDate) {
        console.log(`填入日期: ${startDate} 到 ${endDate}`);

        try {
            // 等待日期输入框出现
            const startInput = await waitForElement('input[date-range="start"]');
            const endInput = await waitForElement('input[date-range="end"]');

            console.log('找到日期输入框');

            // 关闭可能存在的日历面板
            console.log('关闭日历面板...');
            document.body.click();
            await new Promise(resolve => setTimeout(resolve, 300));

            // 直接写入日期
            console.log('=== 直接写入开始日期 ===');
            setDateDirectly(startInput, startDate);

            console.log('=== 直接写入结束日期 ===');
            setDateDirectly(endInput, endDate);

            // 等待一下让React处理
            await new Promise(resolve => setTimeout(resolve, 500));

            // 点击页面空白区域关闭日期面板
            console.log('点击页面空白区域关闭日期面板...');
            document.body.click();
            await new Promise(resolve => setTimeout(resolve, 300));

            // 验证结果
            console.log(`=== 验证结果 ===`);
            console.log(`开始日期值: "${startInput.value}"`);
            console.log(`结束日期值: "${endInput.value}"`);

            // 检查是否设置成功
            if (startInput.value === startDate && endInput.value === endDate) {
                console.log('✅ 日期设置成功！');
            } else {
                console.warn(`⚠️ 日期可能设置不完整，期望: ${startDate} - ${endDate}`);

                // 简单重试一次
                console.log('重试设置日期...');
                setDateDirectly(startInput, startDate);
                setDateDirectly(endInput, endDate);
                await new Promise(resolve => setTimeout(resolve, 300));

                // 再次点击空白区域
                document.body.click();
                await new Promise(resolve => setTimeout(resolve, 300));

                console.log(`重试后开始日期值: "${startInput.value}"`);
                console.log(`重试后结束日期值: "${endInput.value}"`);
            }

            console.log('日期填入完成');

        } catch (error) {
            console.error('填入日期失败:', error);
            throw error;
        }
    }

    // 等待元素出现
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`元素 ${selector} 未找到`));
                } else {
                    setTimeout(check, 100);
                }
            }

            check();
        });
    }

    // 改进的按钮查找函数
    function findButtonByText(text, buttonType = null) {
        console.log(`查找按钮: "${text}", 类型: ${buttonType || '任意'}`);

        const buttons = buttonType ?
            document.querySelectorAll(`button[type="${buttonType}"]`) :
            document.querySelectorAll('button');

        console.log(`找到 ${buttons.length} 个按钮`);

        for (let btn of buttons) {
            const btnText = btn.textContent.trim();
            console.log(`检查按钮文本: "${btnText}"`);

            // 移除多余空格并比较
            const normalizedBtnText = btnText.replace(/\s+/g, ' ');
            const normalizedSearchText = text.replace(/\s+/g, ' ');

            if (normalizedBtnText.includes(normalizedSearchText)) {
                console.log(`✅ 找到匹配按钮: "${btnText}"`);
                return btn;
            }
        }

        console.log(`❌ 未找到包含 "${text}" 的按钮`);
        return null;
    }

    // 自动点击按钮
    async function autoClickButtons() {
        if (!isProcessing || isPaused) return;

        try {
            // 在点击搜索按钮前，验证日期是否还在
            console.log('验证日期是否还在输入框中...');
            const startInput = document.querySelector('input[date-range="start"]');
            const endInput = document.querySelector('input[date-range="end"]');

            if (startInput && endInput) {
                const currentStartDate = startInput.value;
                const currentEndDate = endInput.value;
                console.log(`当前开始日期: "${currentStartDate}"`);
                console.log(`当前结束日期: "${currentEndDate}"`);

                // 如果日期为空或不正确，重新设置
                const expectedStartDate = currentMonth.startDate;
                const expectedEndDate = currentMonth.endDate;

                if (currentStartDate !== expectedStartDate || currentEndDate !== expectedEndDate) {
                    console.warn('⚠️ 检测到日期丢失或不正确，重新设置...');
                    console.log(`期望: ${expectedStartDate} - ${expectedEndDate}`);
                    console.log(`实际: ${currentStartDate} - ${currentEndDate}`);

                    // 重新设置日期
                    setDateDirectly(startInput, expectedStartDate);
                    await new Promise(resolve => setTimeout(resolve, 300));
                    setDateDirectly(endInput, expectedEndDate);
                    await new Promise(resolve => setTimeout(resolve, 300));

                    // 点击空白区域关闭面板
                    document.body.click();
                    await new Promise(resolve => setTimeout(resolve, 500));

                    console.log(`重新设置后开始日期: "${startInput.value}"`);
                    console.log(`重新设置后结束日期: "${endInput.value}"`);
                } else {
                    console.log('✅ 日期验证通过');
                }
            }

            console.log('等待搜索按钮...');
            // 查找搜索按钮 - 尝试多种方式
            let searchBtn = null;
            let attempts = 0;
            while (!searchBtn && attempts < 50 && !isPaused) {
                // 方式1: 通过文本查找
                searchBtn = findButtonByText('搜 索', 'submit');

                // 方式2: 如果方式1失败，尝试查找所有submit按钮
                if (!searchBtn) {
                    const submitButtons = document.querySelectorAll('button[type="submit"]');
                    console.log(`找到 ${submitButtons.length} 个submit按钮`);
                    for (let btn of submitButtons) {
                        const btnText = btn.textContent.trim();
                        console.log(`Submit按钮文本: "${btnText}"`);
                        if (btnText.includes('搜') || btnText.includes('查')) {
                            searchBtn = btn;
                            console.log('✅ 通过关键字找到搜索按钮');
                            break;
                        }
                    }
                }

                // 方式3: 通过XPath查找（如果前面都失败）
                if (!searchBtn) {
                    try {
                        const xpath = "/html/body/div[1]/div/div[2]/div/div/div/div/div/div[2]/div[2]/main/div/div[2]/div/div/div/form/div[13]/div/div/div/div/button[1]";
                        const result = document.evaluate(xpath, document, null, XPathResult.FIRST_ORDERED_NODE_TYPE, null);
                        if (result.singleNodeValue) {
                            searchBtn = result.singleNodeValue;
                            console.log('✅ 通过XPath找到搜索按钮');
                        }
                    } catch (e) {
                        console.log('XPath查找失败:', e);
                    }
                }

                if (!searchBtn) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                    attempts++;
                }
            }

            if (isPaused) return;

            if (!searchBtn) {
                throw new Error('搜索按钮未找到');
            }

            console.log(`找到搜索按钮，文本: "${searchBtn.textContent.trim()}"`);

            // 使用模拟鼠标点击
            console.log('使用模拟鼠标点击搜索按钮...');
            simulateMouseClick(searchBtn);
            console.log('已模拟点击搜索按钮');

            // 点击后立即检查日期是否还在
            await new Promise(resolve => setTimeout(resolve, 1000));
            console.log('检查点击搜索按钮后日期是否还在...');
            const afterClickStartDate = startInput ? startInput.value : '';
            const afterClickEndDate = endInput ? endInput.value : '';
            console.log(`点击后开始日期: "${afterClickStartDate}"`);
            console.log(`点击后结束日期: "${afterClickEndDate}"`);

            // 如果日期丢失，重新设置
            if (!afterClickStartDate || !afterClickEndDate) {
                console.warn('🚨 搜索按钮点击后日期丢失，立即重新设置！');
                if (startInput && endInput) {
                    setDateDirectly(startInput, currentMonth.startDate);
                    await new Promise(resolve => setTimeout(resolve, 300));
                    setDateDirectly(endInput, currentMonth.endDate);
                    await new Promise(resolve => setTimeout(resolve, 300));
                    document.body.click();
                    await new Promise(resolve => setTimeout(resolve, 300));

                    // 再次点击搜索按钮
                    console.log('重新设置日期后，再次点击搜索按钮...');
                    simulateMouseClick(searchBtn);
                }
            }

            // 等待4秒，期间检查暂停状态
            for (let i = 0; i < 20; i++) {
                if (isPaused) return;
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            console.log('等待导出按钮...');
            // 查找导出按钮
            let exportBtn = null;
            attempts = 0;
            while (!exportBtn && attempts < 50 && !isPaused) {
                exportBtn = findButtonByText('导出业务数据', 'button');
                if (!exportBtn) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                    attempts++;
                }
            }

            if (isPaused) return;

            if (!exportBtn) {
                throw new Error('导出按钮未找到');
            }

            // 使用模拟鼠标点击
            console.log('使用模拟鼠标点击导出按钮...');
            simulateMouseClick(exportBtn);
            console.log('已模拟点击导出按钮');

            // 等待5秒后处理下一个月，期间检查暂停状态
            for (let i = 0; i < 25; i++) {
                if (isPaused) return;
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            if (!isPaused) {
                currentMonth++;
                processMonth(currentMonth);
            }

        } catch (error) {
            console.error('自动点击失败:', error);
            isProcessing = false;
            isPaused = false;
            updatePauseButton();
            alert('自动点击失败，请检查页面元素');
        }
    }

    // 初始化
    function init() {
        // 创建悬浮按钮
        createFloatingButton();

        // 创建暂停按钮（初始隐藏）
        createPauseButton();
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
