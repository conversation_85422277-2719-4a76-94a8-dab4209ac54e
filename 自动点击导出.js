// ==UserScript==
// @name         Tastien数据导出助手
// @namespace    http://tampermonkey.net/
// @version      1.4.0
// @description  自动导出Tastien平台指定月份范围的业务数据
// <AUTHOR>
// @match        https://tastien.tastientech.com/portal/expand-store/developStores/bunk*
// @grant        none
// @updateURL
// @downloadURL
// ==/UserScript==

/*
 * 版本: 1.6.0
 * 变更记录:
 * - 2025-01-01: 初始版本，实现基础的月份范围数据导出功能
 * - 2025-01-01: 修改为直接在页面日期输入框中填入日期，不再使用URL跳转
 * - 2025-01-01: 修复querySelector重写导致页面冲突的问题，简化按钮查找逻辑
 * - 2025-01-01: 添加暂停按钮功能，确保日期格式为YYYY-MM-DD
 * - 2025-01-01: 修复日期输入问题，改进React组件值设置方法，注意3个月时间跨度限制
 * - 2025-01-01: 重写日期选择逻辑，支持Ant Design日历面板点击选择日期
 * - 2025-01-01: 修复第二个月日期输入问题，改进同月和跨月日期选择逻辑
 * - 2025-01-01: 恢复使用实际月末日期，而不是固定28日
 * - 2025-01-01: 修复第二次输入时开始日期错误输入到结束日期框的问题
 * - 2025-01-01: 重大重构：改为直接输入日期文本，不再使用日历控件点击方式
 * - 2025-01-01: v1.5.0 改进日期输入方式，使用逐字符输入模拟真实用户行为，增强Ant Design兼容性
 * - 2025-01-01: v1.6.0 修复日历面板关闭问题，改进日期输入验证和清理逻辑，增强稳定性
 */

(function() {
    'use strict';

    // 全局变量
    let isProcessing = false;
    let isPaused = false;
    let currentMonth = 1;
    let endMonth = 12;
    let currentYear = new Date().getFullYear();
    let pauseButton = null;


    // 创建悬浮按钮
    function createFloatingButton() {
        const button = document.createElement('div');
        button.id = 'tastien-export-btn';
        button.innerHTML = '📊';
        button.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: #1890ff;
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
        `;

        button.addEventListener('click', showModal);
        document.body.appendChild(button);
    }

    // 创建暂停按钮
    function createPauseButton() {
        pauseButton = document.createElement('div');
        pauseButton.id = 'tastien-pause-btn';
        pauseButton.innerHTML = isPaused ? '▶️' : '⏸️';
        pauseButton.style.cssText = `
            position: fixed;
            top: 80px;
            right: 20px;
            width: 50px;
            height: 50px;
            background: ${isPaused ? '#52c41a' : '#ff4d4f'};
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            z-index: 10000;
            font-size: 16px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            opacity: ${isProcessing ? '1' : '0.3'};
            pointer-events: ${isProcessing ? 'auto' : 'none'};
        `;

        pauseButton.addEventListener('click', togglePause);
        document.body.appendChild(pauseButton);
    }

    // 切换暂停状态
    function togglePause() {
        if (!isProcessing) return;

        isPaused = !isPaused;
        pauseButton.innerHTML = isPaused ? '▶️' : '⏸️';
        pauseButton.style.background = isPaused ? '#52c41a' : '#ff4d4f';

        console.log(isPaused ? '已暂停导出' : '继续导出');

        if (!isPaused) {
            // 如果从暂停状态恢复，继续处理当前月份
            setTimeout(() => processMonth(currentMonth), 1000);
        }
    }

    // 更新暂停按钮状态
    function updatePauseButton() {
        if (pauseButton) {
            pauseButton.style.opacity = isProcessing ? '1' : '0.3';
            pauseButton.style.pointerEvents = isProcessing ? 'auto' : 'none';

            if (!isProcessing) {
                pauseButton.innerHTML = '⏸️';
                pauseButton.style.background = '#ff4d4f';
                isPaused = false;
            }
        }
    }

    // 创建模态框
    function showModal() {
        if (isProcessing) {
            alert('正在处理中，请稍候...');
            return;
        }

        const modal = document.createElement('div');
        modal.id = 'tastien-modal';
        modal.innerHTML = `
            <div style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 10001; display: flex; align-items: center; justify-content: center;">
                <div style="background: white; padding: 20px; border-radius: 8px; width: 350px;">
                    <h3>选择导出月份范围</h3>
                    <div style="margin: 10px 0; padding: 10px; background: #fff7e6; border: 1px solid #ffd591; border-radius: 4px; font-size: 12px;">
                        <strong>注意:</strong> 系统限制时间跨度不能超过3个月，脚本会自动按月逐个处理<br>
                        <strong>日期范围:</strong> 每月从1日到月末（如：2025-01-01 至 2025-01-31）
                    </div>
                    <div style="margin: 10px 0;">
                        <label>开始月份 (1-12): </label>
                        <input type="number" id="start-month" min="1" max="12" value="1" style="width: 60px;">
                    </div>
                    <div style="margin: 10px 0;">
                        <label>结束月份 (1-12): </label>
                        <input type="number" id="end-month" min="1" max="12" value="12" style="width: 60px;">
                    </div>
                    <div style="margin: 20px 0; text-align: center;">
                        <button id="start-export" style="margin-right: 10px; padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">开始导出</button>
                        <button id="cancel-export" style="padding: 8px 16px; background: #ccc; color: black; border: none; border-radius: 4px; cursor: pointer;">取消</button>
                    </div>
                </div>
            </div>
        `;

        document.body.appendChild(modal);

        // 绑定事件
        document.getElementById('start-export').addEventListener('click', startExport);
        document.getElementById('cancel-export').addEventListener('click', () => {
            document.body.removeChild(modal);
        });
    }

    // 开始导出流程
    function startExport() {
        const startMonth = parseInt(document.getElementById('start-month').value);
        const endMonthValue = parseInt(document.getElementById('end-month').value);

        if (startMonth < 1 || startMonth > 12 || endMonthValue < 1 || endMonthValue > 12) {
            alert('请输入有效的月份 (1-12)');
            return;
        }

        if (startMonth > endMonthValue) {
            alert('开始月份不能大于结束月份');
            return;
        }

        // 关闭模态框
        const modal = document.getElementById('tastien-modal');
        document.body.removeChild(modal);

        // 开始处理
        isProcessing = true;
        isPaused = false;
        currentMonth = startMonth;
        endMonth = endMonthValue;

        // 创建暂停按钮
        createPauseButton();
        updatePauseButton();

        console.log(`开始导出 ${startMonth} 月到 ${endMonthValue} 月的数据`);
        processMonth(currentMonth);
    }

    // 处理单个月份
    async function processMonth(month) {
        // 检查是否暂停
        if (isPaused) {
            console.log('导出已暂停');
            return;
        }

        if (month > endMonth) {
            isProcessing = false;
            isPaused = false;
            updatePauseButton();
            alert('所有月份数据导出完成！');
            return;
        }

        console.log(`正在处理 ${month} 月数据...`);

        // 构建日期 - 每月从1日到月末，确保格式为 YYYY-MM-DD
        const startDate = `${currentYear}-${month.toString().padStart(2, '0')}-01`;
        const endDate = `${currentYear}-${month.toString().padStart(2, '0')}-${getLastDayOfMonth(currentYear, month)}`;

        try {
            // 填入日期到输入框
            await fillDateInputs(startDate, endDate);

            // 等待2秒后自动点击按钮
            setTimeout(autoClickButtons, 2000);

        } catch (error) {
            console.error('处理月份失败:', error);
            isProcessing = false;
            isPaused = false;
            updatePauseButton();
            alert(`处理 ${month} 月数据时出错: ${error.message}`);
        }
    }
    // 获取月份最后一天
    function getLastDayOfMonth(year, month) {
        return new Date(year, month, 0).getDate().toString().padStart(2, '0');
    }





    // 改进的逐字符输入模拟函数 - 更接近真实用户行为
    function simulateTyping(input, value) {
        console.log(`开始逐字符输入: ${value}`);

        return new Promise((resolve) => {
            // 完全清空并重置输入框
            input.value = '';
            input.blur();

            // 等待一下再开始
            setTimeout(() => {
                // 聚焦输入框
                input.focus();

                // 触发focus事件
                input.dispatchEvent(new FocusEvent('focus', { bubbles: true }));

                let currentIndex = 0;

                function typeNextChar() {
                    if (currentIndex >= value.length) {
                        // 输入完成，等待一下再触发最终事件
                        setTimeout(() => {
                            input.dispatchEvent(new Event('change', { bubbles: true }));

                            // 验证最终值
                            const finalValue = input.value;
                            console.log(`逐字符输入完成，最终值: "${finalValue}"`);

                            // 失焦
                            input.blur();
                            input.dispatchEvent(new FocusEvent('blur', { bubbles: true }));

                            resolve();
                        }, 100);
                        return;
                    }

                    const char = value[currentIndex];

                    // 触发keydown事件
                    input.dispatchEvent(new KeyboardEvent('keydown', {
                        key: char,
                        code: char === '-' ? 'Minus' : (char >= '0' && char <= '9' ? `Digit${char}` : `Key${char.toUpperCase()}`),
                        bubbles: true
                    }));

                    // 设置值
                    const currentValue = value.substring(0, currentIndex + 1);
                    const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, "value").set;
                    nativeInputValueSetter.call(input, currentValue);

                    // 触发input事件
                    input.dispatchEvent(new InputEvent('input', {
                        data: char,
                        inputType: 'insertText',
                        bubbles: true
                    }));

                    // 触发keyup事件
                    input.dispatchEvent(new KeyboardEvent('keyup', {
                        key: char,
                        code: char === '-' ? 'Minus' : (char >= '0' && char <= '9' ? `Digit${char}` : `Key${char.toUpperCase()}`),
                        bubbles: true
                    }));

                    currentIndex++;

                    // 继续输入下一个字符（稍微慢一点，更稳定）
                    setTimeout(typeNextChar, 80);
                }

                // 开始输入
                typeNextChar();
            }, 200);
        });
    }

    // 备用的直接设置函数
    function simulateDirectInput(input, value) {
        console.log(`使用直接设置方式: ${value}`);

        try {
            // 完全重置输入框
            input.value = '';
            input.blur();

            // 等待一下再开始
            setTimeout(() => {
                // 聚焦输入框
                input.focus();

                // 使用React的原生setter设置值
                const nativeInputValueSetter = Object.getOwnPropertyDescriptor(window.HTMLInputElement.prototype, 'value').set;
                nativeInputValueSetter.call(input, value);

                // 触发完整的事件序列
                const events = [
                    new FocusEvent('focus', { bubbles: true }),
                    new InputEvent('input', {
                        bubbles: true,
                        inputType: 'insertText',
                        data: value
                    }),
                    new Event('change', { bubbles: true })
                ];

                events.forEach(event => {
                    // 设置事件的target属性
                    Object.defineProperty(event, 'target', { value: input, enumerable: true });
                    input.dispatchEvent(event);
                });

                // 等待一下再失焦
                setTimeout(() => {
                    input.blur();
                    input.dispatchEvent(new FocusEvent('blur', { bubbles: true }));

                    const finalValue = input.value;
                    console.log(`直接设置后的值: "${finalValue}"`);
                }, 100);

            }, 100);

            // 延迟返回结果
            setTimeout(() => {
                return input.value === value;
            }, 300);

        } catch (error) {
            console.error('直接设置输入值时出错:', error);
            return false;
        }

        return true; // 暂时返回true，实际验证在后面
    }

    // 强制关闭日历面板的函数
    async function forceCloseDatePicker() {
        console.log('强制关闭日历面板...');

        // 尝试多种方式关闭日历面板
        try {
            // 1. 点击页面空白区域
            document.body.click();
            await new Promise(resolve => setTimeout(resolve, 200));

            // 2. 查找并关闭可能存在的日历面板
            const pickerPanels = document.querySelectorAll('.ant-picker-panel, .ant-picker-dropdown');
            if (pickerPanels.length > 0) {
                console.log(`发现${pickerPanels.length}个日历面板，尝试关闭...`);
                pickerPanels.forEach(panel => {
                    if (panel.style) {
                        panel.style.display = 'none';
                    }
                });
            }

            // 3. 按ESC键
            document.dispatchEvent(new KeyboardEvent('keydown', {
                key: 'Escape',
                code: 'Escape',
                bubbles: true
            }));

            await new Promise(resolve => setTimeout(resolve, 300));

            // 4. 再次点击空白区域确保关闭
            document.body.click();
            await new Promise(resolve => setTimeout(resolve, 200));

            console.log('日历面板关闭操作完成');

        } catch (error) {
            console.warn('关闭日历面板时出错:', error);
        }
    }

    // 改进的日期输入函数 - 支持多种输入方式
    async function fillDateInputs(startDate, endDate) {
        console.log(`填入日期: ${startDate} 到 ${endDate}`);

        try {
            // 等待日期输入框出现
            const startInput = await waitForElement('input[date-range="start"]');
            const endInput = await waitForElement('input[date-range="end"]');

            console.log('找到日期输入框');

            // 强制关闭任何打开的日历面板
            await forceCloseDatePicker();

            // 尝试多种输入方式
            let success = false;
            let attempts = 0;
            const maxAttempts = 3;

            while (!success && attempts < maxAttempts) {
                attempts++;
                console.log(`=== 第${attempts}次尝试输入日期 ===`);

                try {
                    // 每次尝试前都强制关闭日历面板
                    await forceCloseDatePicker();

                    // 清空输入框并重新聚焦
                    console.log('清空并重置输入框...');
                    startInput.value = '';
                    endInput.value = '';
                    startInput.blur();
                    endInput.blur();
                    await new Promise(resolve => setTimeout(resolve, 300));

                    if (attempts === 1) {
                        // 第一次尝试：使用逐字符输入
                        console.log('使用逐字符输入方式...');
                        console.log('=== 设置开始日期 ===');
                        await simulateTyping(startInput, startDate);
                        await new Promise(resolve => setTimeout(resolve, 800));

                        // 再次确保日历面板关闭
                        await forceCloseDatePicker();

                        console.log('=== 设置结束日期 ===');
                        await simulateTyping(endInput, endDate);
                        await new Promise(resolve => setTimeout(resolve, 800));

                    } else {
                        // 后续尝试：使用直接设置方式
                        console.log('使用直接设置方式...');
                        console.log('=== 设置开始日期 ===');
                        const startSuccess = simulateDirectInput(startInput, startDate);
                        await new Promise(resolve => setTimeout(resolve, 800));

                        // 再次确保日历面板关闭
                        await forceCloseDatePicker();

                        console.log('=== 设置结束日期 ===');
                        const endSuccess = simulateDirectInput(endInput, endDate);
                        await new Promise(resolve => setTimeout(resolve, 800));

                        if (!startSuccess || !endSuccess) {
                            console.warn('直接设置方式返回失败，继续验证...');
                        }
                    }

                    // 最终确保日历面板关闭
                    await forceCloseDatePicker();

                    // 验证结果
                    console.log(`=== 验证第${attempts}次尝试结果 ===`);
                    console.log(`开始日期当前值: "${startInput.value}"`);
                    console.log(`结束日期当前值: "${endInput.value}"`);

                    // 检查是否设置成功
                    if (startInput.value === startDate && endInput.value === endDate) {
                        success = true;
                        console.log('✅ 日期设置成功！');
                    } else {
                        console.warn(`❌ 第${attempts}次尝试失败，期望: ${startDate} - ${endDate}`);
                        if (attempts < maxAttempts) {
                            console.log('等待后重试...');
                            await new Promise(resolve => setTimeout(resolve, 1000));
                        }
                    }

                } catch (error) {
                    console.error(`第${attempts}次尝试出错:`, error);
                    if (attempts < maxAttempts) {
                        await new Promise(resolve => setTimeout(resolve, 1000));
                    }
                }
            }

            if (!success) {
                throw new Error(`经过${maxAttempts}次尝试仍无法正确设置日期`);
            }

            console.log('日期填入完成');

        } catch (error) {
            console.error('填入日期失败:', error);
            throw error;
        }
    }

    // 等待元素出现
    function waitForElement(selector, timeout = 10000) {
        return new Promise((resolve, reject) => {
            const startTime = Date.now();

            function check() {
                const element = document.querySelector(selector);
                if (element) {
                    resolve(element);
                } else if (Date.now() - startTime > timeout) {
                    reject(new Error(`元素 ${selector} 未找到`));
                } else {
                    setTimeout(check, 100);
                }
            }

            check();
        });
    }

    // 查找包含特定文本的按钮
    function findButtonByText(text, buttonType = null) {
        const buttons = buttonType ?
            document.querySelectorAll(`button[type="${buttonType}"]`) :
            document.querySelectorAll('button');

        for (let btn of buttons) {
            if (btn.textContent.includes(text)) {
                return btn;
            }
        }
        return null;
    }

    // 自动点击按钮
    async function autoClickButtons() {
        if (!isProcessing || isPaused) return;

        try {
            console.log('等待搜索按钮...');
            // 查找搜索按钮
            let searchBtn = null;
            let attempts = 0;
            while (!searchBtn && attempts < 50 && !isPaused) {
                searchBtn = findButtonByText('搜 索', 'submit');
                if (!searchBtn) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                    attempts++;
                }
            }

            if (isPaused) return;

            if (!searchBtn) {
                throw new Error('搜索按钮未找到');
            }

            searchBtn.click();
            console.log('已点击搜索按钮');

            // 等待5秒，期间检查暂停状态
            for (let i = 0; i < 25; i++) {
                if (isPaused) return;
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            console.log('等待导出按钮...');
            // 查找导出按钮
            let exportBtn = null;
            attempts = 0;
            while (!exportBtn && attempts < 50 && !isPaused) {
                exportBtn = findButtonByText('导出业务数据', 'button');
                if (!exportBtn) {
                    await new Promise(resolve => setTimeout(resolve, 200));
                    attempts++;
                }
            }

            if (isPaused) return;

            if (!exportBtn) {
                throw new Error('导出按钮未找到');
            }

            exportBtn.click();
            console.log('已点击导出按钮');

            // 等待5秒后处理下一个月，期间检查暂停状态
            for (let i = 0; i < 25; i++) {
                if (isPaused) return;
                await new Promise(resolve => setTimeout(resolve, 200));
            }

            if (!isPaused) {
                currentMonth++;
                processMonth(currentMonth);
            }

        } catch (error) {
            console.error('自动点击失败:', error);
            isProcessing = false;
            isPaused = false;
            updatePauseButton();
            alert('自动点击失败，请检查页面元素');
        }
    }

    // 初始化
    function init() {
        // 创建悬浮按钮
        createFloatingButton();

        // 创建暂停按钮（初始隐藏）
        createPauseButton();
    }

    // 页面加载完成后初始化
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', init);
    } else {
        init();
    }

})();
